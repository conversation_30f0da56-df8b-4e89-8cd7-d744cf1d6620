import 'package:agora_rtm/agora_rtm.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_audio_room/core/utils/analytics_utils.dart';
import 'package:flutter_audio_room/features/audio_room/constants/room_constants.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_message/room_message_enums.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_message/room_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_position.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/mixins/audio_room_provider_base_mixin.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/error_handler.dart';

mixin AudioRoomProviderMicMixin on AudioRoomProviderBaseMixin {

  Future<VoidResult> muteAllRemoteAudio(bool muted) async {
    return ErrorHandler.handle(
      action: () async {
        return audioService.muteAllRemoteAudio(muted);
      },
      identifier: 'mute_all_remote_audio',
    );
  }

  /// Accept mic invite
  @protected
  Future<VoidResult> acceptMicInvite(
    int targetUid,
    RoomMessageModel message,
  ) async {
    return ErrorHandler.handle(
      action: () async {
        final result = await audioService.sendChannelMessage(message);

        // Track mic action - accept mic invite
        if (result.isRight()) {
          AnalyticsUtils.trackMicAction(
            action: 'accept_mic_invite',
            roomId: state.currentRoom?.id ?? '',
            seatPosition: message.position?.targetPosition,
            success: true,
          );
        }

        return result;
      },
      identifier: 'accept_mic_invite',
    );
  }

  /// decline mic invite
  /// [targetUid] Target user ID
  /// [message] Message
  @protected
  Future<VoidResult> declineMicInvite(
    int targetUid,
    RoomMessageModel message,
  ) async {
    return ErrorHandler.handle(
      action: () async {
        return audioService.sendChannelMessage(message);
      },
      identifier: 'decline_mic_invite',
    );
  }

  /// Accept mic request
  @protected
  Future<VoidResult> acceptMicRequest({
    required int targetUid,
    required int targetSeat,
  }) async {
    return ErrorHandler.handle(
      action: () async {
        var newSeat = targetSeat;
        // Check if seat is available
        final seatTaken = state.users.keys.any((key) => key == targetSeat);
        if (seatTaken) {
          newSeat = state.firstEmptySeat();
          if (newSeat == -1) {
            return Left(
                ErrorHandler.createValidationError('Seat is already taken'));
          }
        }

        // Update seat metadata
        final setMetadataResult = await audioService.setChannelMultiMetadata(
          [
            MetadataItem(
              key: RtmMetadataKey.seats()[targetSeat],
              value: targetUid.toString(),
            )
          ],
        );
        if (setMetadataResult.isLeft()) return setMetadataResult;

        // Send accept message
        final message = defaultRoomMessage
            .createEventMessage(
              roomId: state.currentRoom?.id ?? '',
              senderId: state.currentUid,
              sender: state.currentUser,
              targetId: targetUid,
              event: RoomMessageEvent.actionMessage,
              eventSubtype: RoomMessageEventSubtype.agreeMicRequest,
            )
            .copyWith(
              position: RoomPosition(
                targetPosition: targetSeat,
                oldPosition: null,
              ),
            );
        final sendMessageResult =
            await audioService.sendChannelMessage(message);
        if (sendMessageResult.isLeft()) return sendMessageResult;

        // Track mic action - accept mic request
        AnalyticsUtils.trackMicAction(
          action: 'accept_mic_request',
          roomId: state.currentRoom?.id ?? '',
          seatPosition: newSeat,
          success: true,
        );

        return const Right(null);
      },
      identifier: 'accept_mic_request',
    );
  }

  /// Decline mic request
  @protected
  Future<VoidResult> declineMicRequest(int? targetUid) async {
    if (targetUid == null) {
      return Left(ErrorHandler.createValidationError('Invalid uid'));
    }
    return ErrorHandler.handle(
      action: () async {
        final message = defaultRoomMessage.createEventMessage(
          roomId: state.currentRoom?.id ?? '',
          senderId: state.currentUser?.uid,
          sender: state.currentUser,
          targetId: targetUid,
          event: RoomMessageEvent.actionMessage,
          eventSubtype: RoomMessageEventSubtype.rejectMicRequest,
        );
        return audioService.sendChannelMessage(message);
      },
      identifier: 'decline_mic_request',
    );
  }

  @protected
  Future<VoidResult> requestMicrophonePermission() async {
    final isGranted = await permissionService.isMicrophonePermissionGranted();
    if (isGranted.isRight()) return const Right(null);

    final result =
        await permissionService.checkAndRequestMicrophonePermission();

    return result;
  }
}
