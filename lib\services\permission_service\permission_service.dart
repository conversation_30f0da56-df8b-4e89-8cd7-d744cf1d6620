import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/app_exception.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionService {
  /// Check and request microphone permission
  /// Returns true if permission is granted, false otherwise
  Future<ResultWithData<PermissionStatus>>
      checkAndRequestMicrophonePermission() async {
    return _checkAndRequestPermission(
      Permission.microphone,
    );
  }

  /// Check and request notification permission
  /// Returns true if permission is granted, false otherwise
  Future<ResultWithData<PermissionStatus>>
      checkAndRequestNotificationPermission() async {
    return _checkAndRequestPermission(
      Permission.notification,
    );
  }

  /// Check if notification permission is granted
  Future<VoidResult> isNotificationPermissionGranted() async {
    try {
      final status = await Permission.notification.status;
      if (status.isGranted) {
        return const Right(null);
      } else {
        return const Left(AppException(
          message: 'Notification permission is not granted',
          identifier: 'PERMISSION_DENIED',
          statusCode: 403,
        ));
      }
    } catch (e) {
      return Left(AppException(
        message: e.toString(),
        identifier: 'PERMISSION_ERROR',
        statusCode: 400,
      ));
    }
  }

  /// Check if microphone permission is granted
  Future<VoidResult> isMicrophonePermissionGranted() async {
    try {
      final status = await Permission.microphone.status;
      if (status.isGranted) {
        return const Right(null);
      } else {
        return const Left(AppException(
          message: 'Microphone permission is not granted',
          identifier: 'PERMISSION_DENIED',
          statusCode: 403,
        ));
      }
    } catch (e) {
      return Left(AppException(
        message: e.toString(),
        identifier: 'PERMISSION_ERROR',
        statusCode: 400,
      ));
    }
  }

  /// Open app settings
  Future<VoidResult> openSettings() async {
    try {
      final result = await openAppSettings();
      if (result) {
        return const Right(null);
      } else {
        return const Left(AppException(
          message: 'Failed to open settings',
          identifier: 'SETTINGS_ERROR',
          statusCode: 500,
        ));
      }
    } catch (e) {
      return Left(AppException(
        message: e.toString(),
        identifier: 'PERMISSION_ERROR',
        statusCode: 400,
      ));
    }
  }

  /// Generic method to check and request any permission
  /// [permission] The permission to check and request
  /// [permissionName] The name of the permission for error messages
  Future<ResultWithData<PermissionStatus>> _checkAndRequestPermission(
    Permission permission,
  ) async {
    try {
      final status = await permission.status;

      // If permission is already granted, return the status
      if (status.isGranted) {
        return Right(status);
      }

      // If permission is restricted, return restricted status
      if (status.isRestricted) {
        return Left(AppException(
          message: '${permission.toString()} permission is restricted',
          identifier: 'PERMISSION_RESTRICTED',
          statusCode: 403,
        ));
      }

      // // If permission is permanently denied, open settings
      if (status.isPermanentlyDenied) {
        return Left(AppException(
          message:
              '${permission.toString()} permission is permanently denied. Please enable it in Settings',
          identifier: 'PERMISSION_PERMANENTLY_DENIED',
          statusCode: 403,
        ));
      }

      // If permission is denied, try to request it
      if (status.isDenied) {
        final result = await permission.request();

        // Handle request result
        if (result.isGranted) {
          return Right(result);
        } else if (result.isPermanentlyDenied) {
          return Left(AppException(
            message:
                '${permission.toString()} permission is permanently denied. Please enable it in Settings',
            identifier: 'PERMISSION_PERMANENTLY_DENIED',
            statusCode: 403,
          ));
        } else {
          return Left(AppException(
            message: '${permission.toString()} permission is denied',
            identifier: 'PERMISSION_DENIED',
            statusCode: 403,
          ));
        }
      }

      // Handle unknown status
      return const Left(AppException(
        message: 'Unknown permission status',
        identifier: 'PERMISSION_UNKNOWN',
        statusCode: 400,
      ));
    } catch (e) {
      return Left(AppException(
        message: e.toString(),
        identifier: 'PERMISSION_ERROR',
        statusCode: 500,
      ));
    }
  }
}
