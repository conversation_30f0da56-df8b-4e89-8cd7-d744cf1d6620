import 'package:flutter_audio_room/core/utils/analytics_utils.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/authentication/data/model/wallet_model.dart';
import 'package:flutter_audio_room/features/purchase/data/datasources/local_product_datasource.dart';
import 'package:flutter_audio_room/features/purchase/data/datasources/remote_product_datasource.dart';
import 'package:flutter_audio_room/features/purchase/domain/entities/purchase_item.dart';
import 'package:flutter_audio_room/features/purchase/domain/models/product.dart';
import 'package:flutter_audio_room/features/purchase/domain/repositories/i_product_repository.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/app_exception.dart';

class ProductRepositoryImpl implements IProductRepository {
  final LocalProductDataSource _localDataSource;
  final RemoteProductDatasource _remoteDataSource;

  ProductRepositoryImpl({
    required LocalProductDataSource localDataSource,
    required RemoteProductDatasource remoteDataSource,
  })  : _localDataSource = localDataSource,
        _remoteDataSource = remoteDataSource;

  /// 通用的重试请求方法
  ///
  /// [operation] 要执行的异步操作
  /// [maxRetries] 最大重试次数
  /// [delayBetweenRetries] 重试之间的延迟时间（毫秒）
  /// 返回操作结果
  Future<ResultWithData<T>> _retryOperation<T>({
    required Future<ResultWithData<T>> Function() operation,
    required int maxRetries,
    int delayBetweenRetries = 1000,
  }) async {
    int retryCount = 0;
    ResultWithData<T> result;

    do {
      // 如果不是第一次尝试，等待指定时间后再重试
      if (retryCount > 0) {
        LogUtils.d(
          '操作失败，正在进行第 $retryCount 次重试...',
          tag: 'ProductRepositoryImpl',
        );
        await Future.delayed(Duration(milliseconds: delayBetweenRetries));
      }

      // 执行操作
      result = await operation();

      // 如果成功，直接返回结果
      if (result.isRight()) {
        if (retryCount > 0) {
          LogUtils.d('重试成功！', tag: 'ProductRepositoryImpl');
        }
        return result;
      }

      retryCount++;
    } while (retryCount < maxRetries);

    // 所有重试都失败后，返回最后一次的错误结果
    LogUtils.d(
      '操作在 $maxRetries 次重试后仍然失败',
      tag: 'ProductRepositoryImpl',
    );
    return result;
  }

  @override
  Future<ProductListResult> getProducts({int retryCount = 2}) async {
    try {
      // 使用重试逻辑获取远程商品数据
      final remoteResult = await _retryOperation<List<Product>>(
        operation: () => _remoteDataSource.getGemProducts(),
        maxRetries: retryCount,
      );

      return remoteResult.fold(
        (exception) {
          // 远程获取失败，回退到本地数据
          LogUtils.w(
            '从服务器获取商品列表失败: ${exception.message}，回退到本地数据',
            tag: 'ProductRepositoryImpl',
          );
          return _getLocalProducts();
        },
        (products) {
          // 远程获取成功，转换为PurchaseItem列表
          final List<PurchaseItem> items = products.map((product) {
            return PurchaseItem(
              product: product,
              productId: product.productId ?? '',
              purchaseToken: '',
            );
          }).toList();
          return Right(items);
        },
      );
    } catch (e) {
      LogUtils.e(
        '获取商品列表时发生错误: $e',
        tag: 'ProductRepositoryImpl',
      );
      return Left(AppException(
        message: e.toString(),
        statusCode: 500,
        identifier: 'PRODUCT_REPOSITORY_ERROR',
      ));
    }
  }
  
  @override
  Future<ProductListResult> getSubscriptions({int retryCount = 2}) async {
    try {
      // 使用重试逻辑获取远程订阅商品数据
      final remoteResult = await _retryOperation<List<Product>>(
        operation: () => _remoteDataSource.getSubscriptionProducts(),
        maxRetries: retryCount,
      );

      return remoteResult.fold(
        (exception) {
          // 远程获取失败，回退到本地数据
          LogUtils.w(
            '从服务器获取订阅列表失败: ${exception.message}，回退到本地数据',
            tag: 'ProductRepositoryImpl',
          );
          return _getLocalSubscriptions();
        },
        (products) {
          // 远程获取成功，转换为PurchaseItem列表
          final List<PurchaseItem> items = products.map((product) {
            return PurchaseItem(
              product: product,
              productId: product.subscriptionId ?? '',
              purchaseToken: '',
            );
          }).toList();
          return Right(items);
        },
      );
    } catch (e) {
      LogUtils.e(
        '获取订阅列表时发生错误: $e',
        tag: 'ProductRepositoryImpl',
      );
      return Left(AppException(
        message: e.toString(),
        statusCode: 500,
        identifier: 'PRODUCT_REPOSITORY_ERROR',
      ));
    }
  }
  
  @override
  Future<VerifyPurchaseResult> verifyPurchase({
    required String verificationToken,
    required String productId,
    required bool isSubscription,
    int retryCount = 2,
  }) async {
    try {
      LogUtils.d(
        '开始验证购买',
        tag: 'ProductRepositoryImpl',
      );

      // 使用重试逻辑发送购买验证请求
      final result = await _retryOperation<WalletModel>(
        operation: () => isSubscription
            ? _remoteDataSource.verifySubscriptionPurchase(
                verificationToken: verificationToken,
                subscriptionId: productId,
              )
            : _remoteDataSource.verifyGemPurchase(
                verificationToken: verificationToken,
                productId: productId,
              ),
        maxRetries: retryCount,
      );

      return result.fold(
        (exception) {
          LogUtils.e(
            '验证购买失败: ${exception.message}',
            tag: 'ProductRepositoryImpl',
          );

          // Track purchase verification failure
          AnalyticsUtils.trackError(
            errorType: isSubscription
                ? 'subscription_verification_failed'
                : 'gem_verification_failed',
            errorMessage: exception.message,
            errorCode: exception.statusCode.toString(),
            context: 'ProductRepositoryImpl.verifyPurchase',
          );

          return Left(exception);
        },
        (data) async {
          LogUtils.i(
            '验证购买成功，收到响应数据',
            tag: 'ProductRepositoryImpl',
          );

          // Track successful purchase
          if (isSubscription) {
            await AnalyticsUtils.trackSubscriptionPurchase(
              subscriptionType: productId,
              duration: _getSubscriptionDuration(productId),
              price:
                  0.0, // Price will be updated from product details if available
              currency: 'USD',
              transactionId: verificationToken,
            );
          } else {
            // For gem purchases, track as virtual item purchase
            await AnalyticsUtils.trackVirtualItemPurchase(
              itemType: 'gems',
              itemName: productId,
              quantity: data.gems,
              price:
                  0.0, // Price will be updated from product details if available
              currency: 'USD',
              transactionId: verificationToken,
            );
          }

          return Right(data);
        },
      );
    } catch (e) {
      LogUtils.e(
        '验证购买过程中发生错误: $e',
        tag: 'ProductRepositoryImpl',
      );
      return Left(AppException(
        message: e.toString(),
        statusCode: 500,
        identifier: 'VERIFY_PURCHASE_ERROR',
      ));
    }
  }

  ProductListResult _getLocalProducts() {
    final localProducts = _localDataSource.getProducts();
    final List<PurchaseItem> items = localProducts.map((product) {
      return PurchaseItem(
        product: product,
        productId: product.productId ?? '',
        purchaseToken: '',
      );
    }).toList();
    return Right(items);
  }
  
  ProductListResult _getLocalSubscriptions() {
    final localSubscriptions = _localDataSource.getSubscriptions();
    final List<PurchaseItem> items = localSubscriptions.map((product) {
      return PurchaseItem(
        product: product,
        productId: product.subscriptionId ?? '',
        purchaseToken: '',
      );
    }).toList();
    return Right(items);
  }

  /// Get subscription duration from product ID
  String _getSubscriptionDuration(String productId) {
    // Parse duration from product ID or use default mapping
    if (productId.contains('monthly') || productId.contains('month')) {
      return 'monthly';
    } else if (productId.contains('yearly') || productId.contains('year')) {
      return 'yearly';
    } else if (productId.contains('weekly') || productId.contains('week')) {
      return 'weekly';
    } else {
      return 'unknown';
    }
  }
}
